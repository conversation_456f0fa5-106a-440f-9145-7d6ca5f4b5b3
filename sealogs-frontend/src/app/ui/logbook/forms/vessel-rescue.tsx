'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'

import {
    CreateEventType_VesselRescue,
    UpdateEventType_VesselRescue,
    CreateCGEventMission,
    UpdateCGEventMission,
    CreateTripEvent,
    UpdateTripEvent,
    CreateMissionTimeline,
    UpdateMissionTimeline,
} from '@/app/lib/graphQL/mutation'
import { GetTripEvent } from '@/app/lib/graphQL/query'
import { getSeaLogsMembersList } from '@/app/lib/actions'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import { ArrowLeft, Pencil } from 'lucide-react'

import { formatDateTime } from '@/app/helpers/dateHelper'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import TripEventModel from '@/app/offline/models/tripEvent'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import EventType_VesselRescueModel from '@/app/offline/models/eventType_VesselRescue'
import CGEventMissionModel from '@/app/offline/models/cgEventMission'
import MissionTimelineModel from '@/app/offline/models/missionTimeline'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Combobox } from '@/components/ui/comboBox'
import { TimePicker } from '@/components/ui/time-picker'
import { Separator } from '@/components/ui/separator'
import { H3, P } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { FormFooter } from '@/components/ui'

export default function VesselRescue({
    geoLocations,
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    locked,
    offline = false,
}: {
    geoLocations: any
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    locked: any
    offline?: boolean
}) {
    const { toast } = useToast()
    const [locations, setLocations] = useState<any>(false)
    const [time, setTime] = useState<any>()
    const [openCommentsDialog, setOpenCommentsDialog] = useState(false)
    const [commentTime, setCommentTime] = useState<any>()
    const [members, setMembers] = useState<any>(false)
    const [content, setContent] = useState<any>()
    const [rescueData, setRescueData] = useState<any>(false)
    const [missionData, setMissionData] = useState<any>(false)
    const [commentData, setCommentData] = useState<any>(false)
    const [timeline, setTimeline] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const memberModel = new SeaLogsMemberModel()
    const tripEventModel = new TripEventModel()
    const vesselRescueModel = new EventType_VesselRescueModel()
    const cgEventMissionModel = new CGEventMissionModel()
    const missionTimelineModel = new MissionTimelineModel()
    const handleTimeChange = (date: any) => {
        setTime(dayjs(date).format('HH:mm'))
    }

    useEffect(() => {
        setRescueData(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setRescueData(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                setRescueData({
                    vesselName: event.eventType_VesselRescue?.vesselName,
                    callSign: event.eventType_VesselRescue?.callSign,
                    pob: event.eventType_VesselRescue?.pob,
                    latitude: event.eventType_VesselRescue?.latitude,
                    longitude: event.eventType_VesselRescue?.longitude,
                    locationDescription:
                        event.eventType_VesselRescue?.locationDescription,
                    vesselLength: event.eventType_VesselRescue?.vesselLength,
                    vesselType: event.eventType_VesselRescue?.vesselType,
                    makeAndModel: event.eventType_VesselRescue?.makeAndModel,
                    color: event.eventType_VesselRescue?.color,
                    ownerName: event.eventType_VesselRescue?.ownerName,
                    phone: event.eventType_VesselRescue?.phone,
                    email: event.eventType_VesselRescue?.email,
                    address: event.eventType_VesselRescue?.address,
                    ownerOnBoard: event.eventType_VesselRescue?.ownerOnBoard,
                    cgMembership: event.eventType_VesselRescue?.cgMembership,
                    locationID: event.eventType_VesselRescue?.vesselLocationID,
                    missionID: event.eventType_VesselRescue?.mission?.id,
                    operationType: event.eventType_VesselRescue?.operationType
                        ? operationType.filter((operation: any) =>
                              event.eventType_VesselRescue?.operationType
                                  .split(',')
                                  .includes(operation.value),
                          )
                        : [],
                    operationDescription:
                        event.eventType_VesselRescue?.operationDescription,
                    vesselTypeDescription:
                        event.eventType_VesselRescue?.vesselTypeDescription,
                })
                // Handle completedAt time format safely
                const completedAt =
                    event.eventType_VesselRescue?.mission?.completedAt
                if (completedAt) {
                    // If it's already in HH:mm format, use it directly
                    if (
                        typeof completedAt === 'string' &&
                        completedAt.match(/^\d{2}:\d{2}$/)
                    ) {
                        setTime(completedAt)
                    } else {
                        // Parse as datetime and extract time
                        const parsedTime = dayjs(completedAt)
                        if (parsedTime.isValid()) {
                            setTime(parsedTime.format('HH:mm'))
                        } else {
                            setTime(undefined)
                        }
                    }
                } else {
                    setTime(undefined)
                }
                setMissionData({
                    missionType:
                        event.eventType_VesselRescue?.mission?.missionType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    description:
                        event.eventType_VesselRescue?.mission?.description,
                    operationOutcome:
                        event.eventType_VesselRescue?.mission?.operationOutcome?.replaceAll(
                            '_',
                            ' ',
                        ),
                    currentLocationID:
                        event.eventType_VesselRescue?.mission?.currentLocation
                            ?.id,
                    operationDescription:
                        event.eventType_VesselRescue?.mission
                            ?.operationDescription,
                })
                setTimeline(
                    event.eventType_VesselRescue?.mission?.missionTimeline
                        ?.nodes,
                )
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setRescueData({
                    vesselName: event.eventType_VesselRescue?.vesselName,
                    callSign: event.eventType_VesselRescue?.callSign,
                    pob: event.eventType_VesselRescue?.pob,
                    latitude: event.eventType_VesselRescue?.latitude,
                    longitude: event.eventType_VesselRescue?.longitude,
                    locationDescription:
                        event.eventType_VesselRescue?.locationDescription,
                    vesselLength: event.eventType_VesselRescue?.vesselLength,
                    vesselType: event.eventType_VesselRescue?.vesselType,
                    makeAndModel: event.eventType_VesselRescue?.makeAndModel,
                    color: event.eventType_VesselRescue?.color,
                    ownerName: event.eventType_VesselRescue?.ownerName,
                    phone: event.eventType_VesselRescue?.phone,
                    email: event.eventType_VesselRescue?.email,
                    address: event.eventType_VesselRescue?.address,
                    ownerOnBoard: event.eventType_VesselRescue?.ownerOnBoard,
                    cgMembership: event.eventType_VesselRescue?.cgMembership,
                    locationID: event.eventType_VesselRescue?.vesselLocationID,
                    missionID: event.eventType_VesselRescue?.mission?.id,
                    operationType: event.eventType_VesselRescue?.operationType
                        ? operationType.filter((operation: any) =>
                              event.eventType_VesselRescue?.operationType
                                  .split(',')
                                  .includes(operation.value),
                          )
                        : [],
                    operationDescription:
                        event.eventType_VesselRescue?.operationDescription,
                    vesselTypeDescription:
                        event.eventType_VesselRescue?.vesselTypeDescription,
                })
                // Handle completedAt time format safely
                const completedAt =
                    event.eventType_VesselRescue?.mission?.completedAt
                if (completedAt) {
                    // If it's already in HH:mm format, use it directly
                    if (
                        typeof completedAt === 'string' &&
                        completedAt.match(/^\d{2}:\d{2}$/)
                    ) {
                        setTime(completedAt)
                    } else {
                        // Parse as datetime and extract time
                        const parsedTime = dayjs(completedAt)
                        if (parsedTime.isValid()) {
                            setTime(parsedTime.format('HH:mm'))
                        } else {
                            setTime(undefined)
                        }
                    }
                } else {
                    setTime(undefined)
                }
                setMissionData({
                    missionType:
                        event.eventType_VesselRescue?.mission?.missionType?.replaceAll(
                            '_',
                            ' ',
                        ),
                    description:
                        event.eventType_VesselRescue?.mission?.description,
                    operationOutcome:
                        event.eventType_VesselRescue?.mission?.operationOutcome?.replaceAll(
                            '_',
                            ' ',
                        ),
                    currentLocationID:
                        event.eventType_VesselRescue?.mission?.currentLocation
                            ?.id,
                    operationDescription:
                        event.eventType_VesselRescue?.mission
                            ?.operationDescription,
                })
                setTimeline(
                    event.eventType_VesselRescue?.mission?.missionTimeline
                        ?.nodes,
                )
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSetMemberList = (members: any) => {
        setMembers(
            members
                ?.filter(
                    (member: any) =>
                        member.archived == false && member.firstName != '',
                )
                ?.map((member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                })),
        )
    }

    if (!offline) {
        getSeaLogsMembersList(handleSetMemberList)
    }
    const offlineGetMembers = async () => {
        const members = await memberModel.getAll()
        handleSetMemberList(members)
    }
    useEffect(() => {
        if (offline) {
            offlineGetMembers()
        }
    }, [offline])
    const handleCommentTimeChange = (date: Date) => {
        setCommentTime(dayjs(date).format('DD/MM/YYYY HH:mm'))
    }

    useEffect(() => {
        if (geoLocations) {
            setLocations(
                geoLocations.map((location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                })),
            )
        }
    }, [geoLocations])

    const vesselTypes = [
        { label: 'Commercial', value: 'Commercial' },
        { label: 'Recreation', value: 'Recreation' },
        // { label: 'Power', value: 'Power' },
        { label: 'Sail', value: 'Sail' },
        { label: 'Paddle crafts', value: 'Paddle crafts' },
        { label: 'PWC', value: 'PWC' },
        { label: 'Other', value: 'Other' },
    ]

    const missions = [
        { label: 'To locate', value: 'To locate' },
        { label: 'To assist', value: 'To assist' },
        { label: 'To save', value: 'To save' },
        { label: 'To rescue', value: 'To rescue' },
        { label: 'To remove', value: 'To remove' },
    ]

    const operationOutcomes = [
        { label: 'Assisted by others', value: 'Assisted by others' },
        { label: 'Assisted on scene', value: 'Assisted on scene' },
        { label: 'Medical treatment', value: 'Medical treatment' },
        { label: 'Safe and well', value: 'Safe and well' },
        { label: 'Not located', value: 'Not located' },
        { label: 'Not recoverable', value: 'Not recoverable' },
        { label: 'Fatality', value: 'Fatality' },
        { label: 'Other', value: 'Other' },
    ]

    const commentTypes = [
        { label: 'General', value: 'General' },
        { label: 'Underway', value: 'Underway' },
        { label: 'On Scene', value: 'On Scene' },
    ]

    const operationType = [
        {
            label: 'Mechanical / equipment failure',
            value: 'Mechanical / equipment failure',
        },
        { label: 'Vessel adrift', value: 'Vessel adrift' },
        { label: 'Vessel aground', value: 'Vessel aground' },
        { label: 'Capsize', value: 'Capsize' },
        { label: 'Vessel requiring tow', value: 'Vessel requiring tow' },
        { label: 'Flare sighting', value: 'Flare sighting' },
        { label: 'Vessel sinking', value: 'Vessel sinking' },
        { label: 'Collision', value: 'Collision' },
        { label: 'Vessel overdue', value: 'Vessel overdue' },
        { label: 'Other', value: 'Other' },
    ]

    const handleSaveComments = async () => {
        if (rescueData?.missionID === undefined) {
            toast({
                title: 'Error',
                description:
                    'Please save the event first in order to create timeline!',
                variant: 'destructive',
            })
            setOpenCommentsDialog(false)
            return
        }
        const variables = {
            input: {
                commentType: commentData?.commentType,
                description: content ? content : '',
                time: commentTime
                    ? commentTime
                    : dayjs().format('DD/MM/YYYY HH:mm'),
                authorID: commentData?.authorID,
                missionID: rescueData?.missionID,
            },
        }
        if (commentData?.id > 0) {
            if (offline) {
                await missionTimelineModel.save({
                    id: commentData?.id,
                    ...variables.input,
                })
                setOpenCommentsDialog(false)
                getCurrentEvent(selectedEvent?.id)
            } else {
                updateMissionTimeline({
                    variables: {
                        input: {
                            id: commentData?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                await missionTimelineModel.save({
                    id: generateUniqueId(),
                    ...variables.input,
                })
                setOpenCommentsDialog(false)
                getCurrentEvent(selectedEvent?.id)
            } else {
                createMissionTimeline({
                    variables: {
                        input: {
                            ...variables.input,
                        },
                    },
                })
            }
        }
    }

    const [createMissionTimeline] = useMutation(CreateMissionTimeline, {
        onCompleted: (response) => {
            setOpenCommentsDialog(false)
            getCurrentEvent(selectedEvent?.id)
        },
        onError: (error) => {
            console.error('Error creating mission timeline', error)
        },
    })

    const [updateMissionTimeline] = useMutation(UpdateMissionTimeline, {
        onCompleted: (response) => {
            setOpenCommentsDialog(false)
            getCurrentEvent(selectedEvent?.id)
        },
        onError: (error) => {
            console.error('Error updating mission timeline', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                vesselName: rescueData.vesselName,
                callSign: rescueData.callSign,
                pob: +rescueData.pob,
                latitude: rescueData.latitude,
                longitude: rescueData.longitude,
                locationDescription: rescueData.locationDescription,
                vesselLength: +rescueData.vesselLength,
                vesselType: rescueData.vesselType,
                makeAndModel: rescueData.makeAndModel,
                color: rescueData.color,
                ownerName: rescueData.ownerName,
                phone: rescueData.phone,
                email: rescueData.email,
                address: rescueData.address,
                ownerOnBoard: rescueData.ownerOnBoard,
                cgMembershipType: 'cgnz',
                cgMembership: rescueData.cgMembership,
                missionID: rescueData.missionID,
                vesselLocationID: rescueData.locationID,
                operationType: rescueData.operationType
                    ?.map((type: any) => type.value)
                    .join(','),
                operationDescription: rescueData.operationDescription,
                vesselTypeDescription: rescueData.vesselTypeDescription,
            },
        }

        const mission = {
            input: {
                missionType: 'VesselRescue',
                description: missionData.description,
                operationDescription: missionData.operationDescription,
                operationOutcome: missionData.operationOutcome,
                completedAt: time,
                currentLocationID: missionData.currentLocationID,
                eventID: 0,
                eventType: 'VesselRescue',
                missionTimeline: [],
            },
        }

        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'VesselRescue',
                    logBookEntrySectionID: currentTrip.id,
                })
                getCurrentEvent(currentEvent?.id)
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'VesselRescue',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            if (offline) {
                const data = await vesselRescueModel.save({
                    id: +selectedEvent?.eventType_VesselRescueID,
                    ...variables.input,
                })
                const missionDataToSave = {
                    missionType: missionData.missionType,
                    description: missionData.description,
                    operationDescription: missionData.operationDescription,
                    operationOutcome: missionData.operationOutcome,
                    completedAt: time,
                    currentLocationID: missionData.currentLocationID,
                    eventID: +data?.id,
                    eventType: 'VesselRescue',
                }
                const id =
                    +rescueData.missionID > 0
                        ? rescueData.missionID
                        : generateUniqueId()
                await cgEventMissionModel.save({
                    id,
                    ...missionDataToSave,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport(currentTrip)
                if (+rescueData.missionID > 0) {
                    updateTripReport({
                        id: tripReport.map((trip: any) => trip.id),
                    })
                } else {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                }
            } else {
                updateEventType_VesselRescue({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_VesselRescueID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'VesselRescue',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(tripEventData)
                const vesselRescueData = await vesselRescueModel.save({
                    id: generateUniqueId(),
                    vesselName: rescueData.vesselName,
                    callSign: rescueData.callSign,
                    pob: +rescueData.pob,
                    latitude: rescueData.latitude,
                    longitude: rescueData.longitude,
                    locationDescription: rescueData.locationDescription,
                    vesselLength: +rescueData.vesselLength,
                    vesselType: rescueData.vesselType,
                    makeAndModel: rescueData.makeAndModel,
                    color: rescueData.color,
                    ownerName: rescueData.ownerName,
                    phone: rescueData.phone,
                    email: rescueData.email,
                    address: rescueData.address,
                    ownerOnBoard: rescueData.ownerOnBoard,
                    cgMembershipType: 'cgnz',
                    cgMembership: rescueData.cgMembership,
                    missionID: rescueData.missionID,
                    vesselLocationID: rescueData.locationID,
                    tripEventID: tripEventData.id,
                    operationType: rescueData.operationType
                        ?.map((type: any) => type.value)
                        .join(','),
                    operationDescription: rescueData.operationDescription,
                    vesselTypeDescription: rescueData.vesselTypeDescription,
                })
                await cgEventMissionModel.save({
                    id: generateUniqueId(),
                    missionType: missionData.missionType,
                    description: missionData.description,
                    operationDescription: missionData.operationDescription,
                    operationOutcome: missionData.operationOutcome,
                    completedAt: time,
                    currentLocationID: missionData.currentLocationID,
                    eventID: +vesselRescueData?.id,
                    eventType: 'VesselRescue',
                })
                await getCurrentEvent(currentEvent?.id)
                updateTripReport(currentTrip)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                await tripEventModel.save({
                    id: currentEvent?.id,
                    eventType_VesselRescueID: vesselRescueData.id,
                })
                await getCurrentEvent(currentEvent?.id)
                closeModal()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'VesselRescue',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createEventType_VesselRescue({
                variables: {
                    input: {
                        vesselName: rescueData.vesselName,
                        callSign: rescueData.callSign,
                        pob: +rescueData.pob,
                        latitude: rescueData.latitude,
                        longitude: rescueData.longitude,
                        locationDescription: rescueData.locationDescription,
                        vesselLength: +rescueData.vesselLength,
                        vesselType: rescueData.vesselType,
                        makeAndModel: rescueData.makeAndModel,
                        color: rescueData.color,
                        ownerName: rescueData.ownerName,
                        phone: rescueData.phone,
                        email: rescueData.email,
                        address: rescueData.address,
                        ownerOnBoard: rescueData.ownerOnBoard,
                        cgMembershipType: 'cgnz',
                        cgMembership: rescueData.cgMembership,
                        missionID: rescueData.missionID,
                        vesselLocationID: rescueData.locationID,
                        tripEventID: data.id,
                        operationType: rescueData.operationType
                            ?.map((type: any) => type.value)
                            .join(','),
                        operationDescription: rescueData.operationDescription,
                        vesselTypeDescription: rescueData.vesselTypeDescription,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_VesselRescue] = useMutation(
        CreateEventType_VesselRescue,
        {
            onCompleted: (response) => {
                const data = response.createEventType_VesselRescue
                createCGEventMission({
                    variables: {
                        input: {
                            missionType: missionData.missionType,
                            description: missionData.description,
                            operationDescription:
                                missionData.operationDescription,
                            operationOutcome: missionData.operationOutcome,
                            completedAt: time,
                            currentLocationID: missionData.currentLocationID,
                            eventID: +data?.id,
                            eventType: 'VesselRescue',
                        },
                    },
                })
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_VesselRescueID: data.id,
                        },
                    },
                })
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating vessel rescue', error)
            },
        },
    )

    const [updateEventType_VesselRescue] = useMutation(
        UpdateEventType_VesselRescue,
        {
            onCompleted: (response) => {
                const data = response.updateEventType_VesselRescue
                if (rescueData.missionID > 0) {
                    updateCGEventMission({
                        variables: {
                            input: {
                                id: rescueData.missionID,
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID:
                                    missionData.currentLocationID,
                                eventID: +data?.id,
                                eventType: 'VesselRescue',
                            },
                        },
                    })
                } else {
                    createCGEventMission({
                        variables: {
                            input: {
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID:
                                    missionData.currentLocationID,
                                eventID: +data?.id,
                                eventType: 'VesselRescue',
                            },
                        },
                    })
                }
            },
            onError: (error) => {
                console.error('Error updating vessel rescue', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const [createCGEventMission] = useMutation(CreateCGEventMission, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport(currentTrip)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error creating CG Event Mission', error)
        },
    })

    const [updateCGEventMission] = useMutation(UpdateCGEventMission, {
        onCompleted: (response) => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport(currentTrip)
            updateTripReport({
                id: tripReport.map((trip: any) => trip.id),
            })
        },
        onError: (error) => {
            console.error('Error updating CG Event Mission', error)
        },
    })

    return (
        <>
            <div className="space-y-8">
                <div className="space-y-8">
                    <div>
                        <H3>Target vessel and details</H3>
                        <P>
                            Record vessel name and callsign and include number
                            of people on board
                        </P>
                    </div>
                    {operationType && (
                        <Combobox
                            id="operation-type"
                            options={operationType.map((opt: any) => ({
                                value: opt.value,
                                label: opt.label,
                            }))}
                            multi={true}
                            value={rescueData?.operationType}
                            onChange={(value: any) => {
                                setRescueData({
                                    ...rescueData,
                                    operationType: value,
                                })
                            }}
                            placeholder="Operation Type"
                            disabled={locked}
                        />
                    )}
                    {rescueData?.operationType?.find(
                        (operation: any) => operation.value == 'Other',
                    ) && (
                        <div>
                            <Textarea
                                id={`operation-description`}
                                rows={4}
                                placeholder="Operation description"
                                value={rescueData?.operationDescription ?? ''}
                                disabled={locked}
                                onChange={(data: any) => {
                                    setRescueData({
                                        ...rescueData,
                                        operationDescription: data.target.value,
                                    })
                                }}
                            />
                        </div>
                    )}
                    <Label label="Vessel Name">
                        <Input
                            id="vessel-name"
                            type="text"
                            placeholder="Vessel Name"
                            value={rescueData?.vesselName}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    vesselName: e.target.value,
                                })
                            }}
                        />
                    </Label>
                    <Label label="Call Sign">
                        <Input
                            id="call-sign"
                            type="text"
                            placeholder="Call Sign"
                            value={rescueData?.callSign}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    callSign: e.target.value,
                                })
                            }}
                        />
                    </Label>

                    <Label label="People on Board">
                        <Input
                            id="pob"
                            type="number"
                            placeholder="Enter POB"
                            min={1}
                            value={rescueData?.pob}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    pob: e.target.value,
                                })
                            }}
                        />
                    </Label>
                </div>
                <Separator />
                <div className="space-y-8">
                    <div>
                        <H3>Vessel location</H3>
                        <P>
                            Record the approximate location of vessel requiring
                            assistance
                        </P>
                    </div>
                    <Label label="Vessel position">
                        <div className="flex gap-4">
                            <Input
                                id="location-lat"
                                type="text"
                                placeholder="Latitude"
                                value={rescueData?.latitude ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        latitude: e.target.value,
                                    })
                                }}
                            />
                            <Input
                                id="location-long"
                                type="text"
                                placeholder="Longitude"
                                value={rescueData?.longitude ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        longitude: e.target.value,
                                    })
                                }}
                            />
                        </div>
                    </Label>
                    {locations && (
                        <Combobox
                            id="geo-location"
                            options={locations.map((loc: any) => ({
                                value: loc.value,
                                label: loc.label,
                            }))}
                            value={
                                locations?.find(
                                    (location: any) =>
                                        location.value ==
                                        rescueData?.locationID,
                                ) || null
                            }
                            onChange={(value: any) => {
                                setRescueData({
                                    ...rescueData,
                                    locationID: value?.value,
                                })
                            }}
                            placeholder="Select location"
                            disabled={locked}
                        />
                    )}
                    <Textarea
                        id="location-description"
                        rows={4}
                        placeholder="Location description"
                        value={rescueData?.locationDescription}
                        disabled={locked}
                        onChange={(e) => {
                            setRescueData({
                                ...rescueData,
                                locationDescription: e.target.value,
                            })
                        }}
                    />
                </div>
                <Separator />
                <div className="space-y-8">
                    <div>
                        <H3>Vessel description</H3>
                        <P>
                            Include details of vessel type, make and descriptors
                        </P>
                    </div>
                    <Label
                        label="Vessel Length"
                        htmlFor="vessel-length"
                        className="my-4">
                        <Input
                            id="vessel-length"
                            type="number"
                            placeholder="Vessel Length"
                            value={rescueData?.vesselLength ?? 0}
                            min={1}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    vesselLength: e.target.value,
                                })
                            }}
                        />
                    </Label>
                    <Combobox
                        id="vessel-type"
                        options={vesselTypes.map((type: any) => ({
                            value: type.value,
                            label: type.label,
                        }))}
                        value={
                            vesselTypes?.find(
                                (type: any) =>
                                    type.value == rescueData?.vesselType,
                            ) || null
                        }
                        onChange={(value: any) => {
                            setRescueData({
                                ...rescueData,
                                vesselType: value?.value,
                            })
                        }}
                        placeholder="Vessel Type"
                        disabled={locked}
                    />
                    {rescueData?.vesselType == 'Other' && (
                        <div className="my-4">
                            <Textarea
                                id="vessel-type-description"
                                rows={4}
                                placeholder="Vessel Description"
                                value={rescueData?.vesselTypeDescription ?? ''}
                                onChange={(data: any) => {
                                    setRescueData({
                                        ...rescueData,
                                        vesselTypeDescription:
                                            data.target.value,
                                    })
                                }}
                            />
                        </div>
                    )}
                    <Label
                        label="Make and model"
                        htmlFor="make"
                        className="my-4">
                        <Input
                            id="make"
                            type="text"
                            placeholder="Make and model"
                            value={rescueData?.makeAndModel ?? ''}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    makeAndModel: e.target.value,
                                })
                            }}
                        />
                    </Label>
                    <Label label="Color" htmlFor="color" className="my-4">
                        <Input
                            id="color"
                            type="text"
                            placeholder="Color"
                            value={rescueData?.color ?? ''}
                            disabled={locked}
                            onChange={(e) => {
                                setRescueData({
                                    ...rescueData,
                                    color: e.target.value,
                                })
                            }}
                        />
                    </Label>
                </div>
                <Separator />
                <div className=" space-y-8">
                    <div>
                        <H3>Owners details</H3>
                        <P>
                            Record vessel owners’ details and membership number
                            if applicable
                        </P>
                    </div>
                    <div className="flex gap-4">
                        <Label
                            label="Owner's name"
                            htmlFor="owner-name"
                            className="flex-1">
                            <Input
                                id="owner-name"
                                type="text"
                                placeholder="Owner's name"
                                value={rescueData?.ownerName ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        ownerName: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                        <Label
                            label="Phone number"
                            htmlFor="owner-phone"
                            className="flex-1">
                            <Input
                                id="owner-phone"
                                type="text"
                                placeholder="Phone number"
                                value={rescueData?.phone ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        phone: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    </div>
                    <div className="flex gap-4">
                        <Label
                            label="Coastguard NZ membership"
                            htmlFor="cgnz"
                            className="flex-1">
                            <Input
                                id="cgnz"
                                type="text"
                                placeholder="Coastguard NZ membership"
                                value={rescueData?.cgMembership ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        cgMembership: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                        <Label
                            label="Email address"
                            htmlFor="owner-email"
                            className="flex-1">
                            <Input
                                id="owner-email"
                                type="text"
                                placeholder="Email Address"
                                value={rescueData?.email ?? ''}
                                disabled={locked}
                                onChange={(e) => {
                                    setRescueData({
                                        ...rescueData,
                                        email: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    </div>
                    <Label
                        label="Owner's address"
                        htmlFor="owner-address"
                        className="my-4">
                        <Textarea
                            id="owner-address"
                            rows={4}
                            className={''}
                            placeholder="Owner's address"
                            value={rescueData?.address ?? ''}
                            disabled={locked}
                            onChange={(data: any) => {
                                setRescueData({
                                    ...rescueData,
                                    address: data.target.value,
                                })
                            }}
                        />
                    </Label>
                    <CheckFieldLabel
                        type="checkbox"
                        id="owner-on-board"
                        checked={rescueData?.ownerOnBoard}
                        onCheckedChange={(checked) => {
                            setRescueData({
                                ...rescueData,
                                ownerOnBoard: checked,
                            })
                        }}
                        disabled={locked}
                        variant="warning"
                        label="Is the owner on-board?"
                    />
                </div>
                <Separator />
                <div className="space-y-8">
                    <div>
                        <H3>Mission</H3>
                        <P>Mission details and timeline</P>
                    </div>
                    <Combobox
                        id="mission"
                        options={missions.map((mission: any) => ({
                            value: mission.value,
                            label: mission.label,
                        }))}
                        value={
                            missions?.find(
                                (mission: any) =>
                                    mission.value == missionData?.missionType,
                            ) || null
                        }
                        onChange={(value: any) => {
                            setMissionData({
                                ...missionData,
                                missionType: value?.value,
                            })
                        }}
                        placeholder="Mission Type"
                        disabled={locked}
                    />
                    <Textarea
                        id="mission-description"
                        rows={4}
                        placeholder="Mission description"
                        value={missionData?.description ?? ''}
                        disabled={locked}
                        onChange={(e) => {
                            setMissionData({
                                ...missionData,
                                description: e.target.value,
                            })
                        }}
                    />
                    <div className="flex flex-col">
                        <Label>Mission timeline</Label>
                        <div className="flex gap-4 flex-col">
                            {timeline &&
                                timeline?.map((comment: any, index: number) => (
                                    <div
                                        key={index}
                                        className="flex flex-col gap-4 w-full mb-2">
                                        <div className="flex gap-4 justify-between">
                                            <div
                                                className="comment-html"
                                                dangerouslySetInnerHTML={{
                                                    __html: comment.description,
                                                }}></div>
                                            <div className="flex gap-4">
                                                {comment.author.id > 0 && (
                                                    <P>
                                                        {comment.author
                                                            .firstName +
                                                            ' ' +
                                                            comment.author
                                                                .surname}
                                                    </P>
                                                )}
                                                <P>
                                                    {formatDateTime(
                                                        comment.time,
                                                    )}
                                                </P>
                                                <Button
                                                    size="icon"
                                                    variant="ghost"
                                                    onClick={() => {
                                                        setOpenCommentsDialog(
                                                            true,
                                                        )
                                                        setCommentData(comment)
                                                        handleEditorChange(
                                                            comment.description,
                                                        )
                                                        setCommentTime(
                                                            dayjs(
                                                                comment.time,
                                                            ).format(
                                                                'DD/MM/YYYY HH:mm',
                                                            ),
                                                        )
                                                    }}>
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                        </div>
                        <div>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setOpenCommentsDialog(true)
                                    handleEditorChange('')
                                    setCommentData(false)
                                }}>
                                Add Notes/Comments
                            </Button>
                        </div>
                    </div>
                </div>
                <Separator />
                <div className="space-y-8">
                    <div>
                        <H3>Mission complete</H3>
                        <P>
                            Record the operation outcome, location and time of
                            completion
                        </P>
                    </div>
                    <Combobox
                        id="operation-outcome"
                        options={operationOutcomes.map((outcome: any) => ({
                            value: outcome.value,
                            label: outcome.label,
                        }))}
                        value={
                            operationOutcomes?.find(
                                (outcome: any) =>
                                    outcome.value ==
                                    missionData?.operationOutcome,
                            ) || null
                        }
                        onChange={(value: any) => {
                            setMissionData({
                                ...missionData,
                                operationOutcome: value?.value,
                            })
                        }}
                        placeholder="Operation outcome"
                        disabled={locked}
                    />
                    {missionData?.operationOutcome == 'Other' && (
                        <Textarea
                            id="operation-outcome-description"
                            rows={4}
                            placeholder="Description"
                            value={missionData?.operationDescription ?? ''}
                            disabled={locked}
                            onChange={(e) => {
                                setMissionData({
                                    ...missionData,
                                    operationDescription: e.target.value,
                                })
                            }}
                        />
                    )}
                    <TimePicker
                        label="Time of completion"
                        value={
                            time
                                ? (() => {
                                      // Handle different time formats
                                      if (typeof time === 'string') {
                                          // If it's already in HH:mm format
                                          if (time.match(/^\d{2}:\d{2}$/)) {
                                              const timeDate = dayjs(
                                                  `${dayjs().format('YYYY-MM-DD')} ${time}`,
                                              )
                                              return timeDate.isValid()
                                                  ? timeDate.toDate()
                                                  : new Date()
                                          }
                                          // If it's a full datetime string
                                          const parsedTime = dayjs(time)
                                          return parsedTime.isValid()
                                              ? parsedTime.toDate()
                                              : new Date()
                                      }
                                      // If it's already a Date object
                                      return dayjs(time).isValid()
                                          ? dayjs(time).toDate()
                                          : new Date()
                                  })()
                                : new Date()
                        }
                        onChange={(date: Date) => {
                            handleTimeChange(date)
                        }}
                        use24Hour={true}
                        disabled={locked}
                        className="w-full"
                        nowButton={true}
                        nowButtonLabel="Set To Now"
                    />
                    {locations && (
                        <Combobox
                            id="completed-geo-location"
                            options={locations.map((location: any) => ({
                                value: location.value,
                                label: location.label,
                            }))}
                            value={
                                locations?.find(
                                    (location: any) =>
                                        location.value ==
                                        missionData?.currentLocationID,
                                ) || null
                            }
                            onChange={(value: any) => {
                                setMissionData({
                                    ...missionData,
                                    currentLocationID: value?.value,
                                })
                            }}
                            placeholder="Current Location"
                            disabled={locked}
                        />
                    )}
                </div>
                <FormFooter>
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={closeModal}>
                        Cancel
                    </Button>
                    <Button disabled={locked} onClick={handleSave}>
                        {selectedEvent ? 'Update' : 'Save'}
                    </Button>
                </FormFooter>
            </div>

            <AlertDialogNew
                openDialog={openCommentsDialog}
                setOpenDialog={setOpenCommentsDialog}
                handleCreate={handleSaveComments}
                title={
                    commentData?.id > 0
                        ? 'Update Comment'
                        : 'Create New Comment'
                }
                size="xl"
                description={
                    commentData?.id > 0
                        ? 'Edit the comment details below.'
                        : 'Add a new comment to the mission timeline.'
                }
                actionText={commentData?.id > 0 ? 'Update' : 'Create Comment'}
                cancelText="Cancel">
                <div className="my-4 flex items-center">
                    <Combobox
                        id="comment-type"
                        options={commentTypes.map((type: any) => ({
                            value: type.value,
                            label: type.label,
                        }))}
                        value={
                            commentTypes?.find(
                                (type: any) =>
                                    type.value ==
                                    commentData?.commentType?.replaceAll(
                                        '_',
                                        ' ',
                                    ),
                            ) || null
                        }
                        onChange={(value: any) =>
                            setCommentData({
                                ...commentData,
                                commentType: value?.value,
                            })
                        }
                        placeholder="Comment type"
                    />
                </div>
                <div className="mb-4 flex items-center">
                    <TimePicker
                        value={
                            commentTime
                                ? dayjs(
                                      commentTime,
                                      'DD/MM/YYYY HH:mm',
                                  ).toDate()
                                : new Date()
                        }
                        onChange={handleCommentTimeChange}
                        use24Hour={true}
                        className="w-full"
                    />
                </div>

                <Editor
                    id="comment"
                    placeholder="Comment"
                    className="w-full"
                    content={content}
                    handleEditorChange={handleEditorChange}
                />

                <div className="flex items-center">
                    {members && (
                        <Combobox
                            id="comment-author"
                            options={members.map((member: any) => ({
                                value: member.value,
                                label: member.label,
                            }))}
                            value={
                                members?.find(
                                    (member: any) =>
                                        member.value == commentData?.author?.id,
                                ) || null
                            }
                            onChange={(value: any) =>
                                setCommentData({
                                    ...commentData,
                                    authorID: value?.value,
                                })
                            }
                            placeholder="Crew member"
                        />
                    )}
                </div>
            </AlertDialogNew>
        </>
    )
}
