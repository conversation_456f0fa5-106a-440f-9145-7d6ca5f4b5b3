'use client'

import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react'
import Creatable from 'react-select/creatable'

import {
    UpdateDangerousGoodsChecklist,
    CreateDangerousGoodsChecklist,
    CreateMitigationStrategy,
    UpdateMitigationStrategy,
    CreateRiskFactor,
    UpdateRiskFactor,
} from '@/app/lib/graphQL/mutation'
import {
    GetOneDangerousGoodsChecklist,
    GetRiskFactors,
    CrewMembers_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'

import { useSearchParams } from 'next/navigation'
import { getLogBookEntryByID } from '@/app/lib/actions'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import DangerousGoodsChecklistModel from '@/app/offline/models/dangerousGoodsChecklist'
import RiskFactorModel from '@/app/offline/models/riskFactor'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import MitigationStrategyModel from '@/app/offline/models/mitigationStrategy'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { Combobox } from '@/components/ui/comboBox'

import {
    RiskAnalysisSheet,
    StrategyDialog,
} from '@/components/ui/risk-analysis'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'

interface PVPDRiskAnalysisProps {
    onSidebarClose?: () => void
    currentTrip?: any
    crewMembers?: any
    open: boolean
    onOpenChange?: (open: boolean) => void
    editDGR?: boolean
    currentEvent: any
    offline?: boolean
    setAllChecked: (checked: boolean) => void
    onRefreshEvent?: (eventId: string) => void
}

interface RiskBuffer {
    vesselSecuredToWharf?: 'on' | 'off'
    bravoFlagRaised?: 'on' | 'off'
    twoCrewLoadingVessel?: 'on' | 'off'
    fireHosesRiggedAndReady?: 'on' | 'off'
    noSmokingSignagePosted?: 'on' | 'off'
    spillKitAvailable?: 'on' | 'off'
    fireExtinguishersAvailable?: 'on' | 'off'
    dgDeclarationReceived?: 'on' | 'off'
    loadPlanReceived?: 'on' | 'off'
    msdsAvailable?: 'on' | 'off'
    anyVehiclesSecureToVehicleDeck?: 'on' | 'off'
    safetyAnnouncement?: 'on' | 'off'
    vehicleStationaryAndSecure?: 'on' | 'off'
    memberID?: number
}

export default function PVPDRiskAnalysis({
    onSidebarClose,
    currentTrip,
    crewMembers = false,
    open,
    onOpenChange,
    editDGR = false,
    currentEvent,
    offline = false,
    setAllChecked,
    onRefreshEvent,
}: PVPDRiskAnalysisProps) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0

    const [riskAnalysis, setRiskAnalysis] = useState<any>(false)
    const [riskBuffer, setRiskBuffer] = useState<RiskBuffer | false>(false)
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>(false)
    const [content, setContent] = useState<any>('')
    const [allRisks, setAllRisks] = useState<any>(false)
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [riskValue, setRiskValue] = useState<any>(null)
    const [checklistId, setChecklistId] = useState<string>('0')
    const checklistIdRef = useRef<string>('0')

    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [currentStrategies, setCurrentStrategies] = useState<any>([])

    const [riskToDelete, setRiskToDelete] = useState<any>(false)
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [logbook, setLogbook] = useState<any>(false)
    const [members, setMembers] = useState<any>([])

    const { toast } = useToast()

    // Helper function to check if riskBuffer is a valid object
    const isRiskBufferValid = (
        buffer: RiskBuffer | false,
    ): buffer is RiskBuffer => {
        return buffer !== false && typeof buffer === 'object'
    }

    // Memoize model instances to prevent recreation on every render
    const models = useMemo(
        () => ({
            logBookEntry: new LogBookEntryModel(),
            dangerousGoodsChecklist: new DangerousGoodsChecklistModel(),
            riskFactor: new RiskFactorModel(),
            crewMember: new CrewMembers_LogBookEntrySectionModel(),
            mitigationStrategy: new MitigationStrategyModel(),
        }),
        [],
    )

    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                let data = response.readCrewMembers_LogBookEntrySections.nodes
                const crewMembers = data
                    .map((member: any) => {
                        return {
                            label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                            value: member.crewMember.id,
                        }
                    })
                    .filter((member: any) => member.value != logbook.master.id)
                setMembers([...members, ...crewMembers])
            },
            onError: (error: any) => {
                console.error('CrewMembers_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const master = {
            label: `${logbook.master.firstName ?? ''} ${logbook.master.surname ?? ''}`,
            value: logbook.master.id,
        }
        setMembers([master])
        const sections = logbook.logBookEntrySections.nodes.filter(
            (node: any) =>
                node.className === 'SeaLogs\\CrewMembers_LogBookEntrySection',
        )
        if (sections) {
            const sectionIDs = sections.map((section: any) => section.id)
            if (sectionIDs?.length > 0) {
                if (offline) {
                    const data = await models.crewMember.getByIds(sectionIDs)
                    const crewMembers = data
                        .map((member: any) => {
                            return {
                                label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                                value: member.crewMember.id,
                            }
                        })
                        .filter(
                            (member: any) => member.value != logbook.master.id,
                        )
                    setMembers([...members, ...crewMembers])
                } else {
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: { id: { in: sectionIDs } },
                        },
                    })
                }
            }
        }
    }

    if (+logentryID > 0 && !offline) {
        getLogBookEntryByID(+logentryID, handleSetLogbook)
    }

    const offlineUseEffect = async () => {
        const data = await models.logBookEntry.getById(+logentryID)
        handleSetLogbook(data)
    }

    useEffect(() => {
        if (offline) {
            offlineUseEffect()
        }
    }, [offline])

    useEffect(() => {
        if (crewMembers) {
            setMembers(crewMembers)
        }
    }, [crewMembers])

    const [updateDangerousGoodsChecklist] = useMutation(
        UpdateDangerousGoodsChecklist,
        {
            onCompleted: () => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const [updateDangerousGoodsChecklistNoRefresh] = useMutation(
        UpdateDangerousGoodsChecklist,
        {
            onCompleted: () => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const [createDangerousGoodsChecklist] = useMutation(
        CreateDangerousGoodsChecklist,
        {
            onCompleted: (response) => {
                const data = response.createDangerousGoodsChecklist

                // Update the local checklist ID state
                setChecklistId(data.id)
                checklistIdRef.current = data.id

                // Note: currentEvent is read-only, so we rely on the parent refresh callback
                // Refresh the parent component's event data
                if (onRefreshEvent) {
                    onRefreshEvent(currentEvent.id)
                }
                // Reload the risk analysis data

                getRiskAnalysis({
                    variables: {
                        id: data.id,
                    },
                })
            },
            onError: (error) => {
                console.error('Error creating dangerous goods checklist', error)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Failed to create dangerous goods checklist',
                })
            },
        },
    )

    const handleDgrFieldChange = useCallback(
        (field: string) => async (check: boolean) => {
            if (!editDGR) {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                        'You do not have permission to edit this section',
                })
                return
            }

            setRiskBuffer((prev: RiskBuffer | false) => {
                const newBuffer = {
                    ...(prev || {}),
                    [field]: check ? 'on' : 'off',
                } as RiskBuffer

                return newBuffer
            })

            const currentChecklistId = checklistIdRef.current
            if (+currentChecklistId > 0) {
                if (offline) {
                    await models.dangerousGoodsChecklist.save({
                        id: currentChecklistId,
                        [field]: check,
                    })
                } else {
                    updateDangerousGoodsChecklistNoRefresh({
                        variables: {
                            input: {
                                id: currentChecklistId,
                                [field]: check,
                            },
                        },
                    })
                }
            } else {
                if (!offline) {
                    createDangerousGoodsChecklist({
                        variables: {
                            input: {
                                tripReport_StopID: currentEvent.id,
                                [field]: check,
                            },
                        },
                    })
                }
            }
        },
        [
            editDGR,
            toast,
            currentEvent.id,
            offline,
            models.dangerousGoodsChecklist,
            updateDangerousGoodsChecklistNoRefresh,
            createDangerousGoodsChecklist,
        ],
    )

    const fields = useMemo(
        () => [
            {
                name: 'VesselSecuredToWharf',
                label: 'Vessel secured to wharf',
                value: 'vesselSecuredToWharf',
                checked:
                    riskBuffer &&
                    typeof riskBuffer === 'object' &&
                    riskBuffer.vesselSecuredToWharf
                        ? riskBuffer.vesselSecuredToWharf === 'on'
                        : riskAnalysis?.vesselSecuredToWharf,
                handleChange: handleDgrFieldChange('vesselSecuredToWharf'),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'BravoFlagRaised',
                label: 'Bravo flag raised',
                value: 'bravoFlagRaised',
                checked:
                    isRiskBufferValid(riskBuffer) && riskBuffer.bravoFlagRaised
                        ? riskBuffer.bravoFlagRaised === 'on'
                        : riskAnalysis?.bravoFlagRaised,
                handleChange: handleDgrFieldChange('bravoFlagRaised'),
                description: (
                    <small>
                        <div>
                            Ascertain the nature of the problem, any damage, or
                            taking on water.
                        </div>
                        <div>
                            Does a crew member need to go on board the other
                            vessel to assist?
                        </div>
                    </small>
                ),
            },
            {
                name: 'TwoCrewLoadingVessel',
                label: 'Two crew loading vessel',
                value: 'twoCrewLoadingVessel',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.twoCrewLoadingVessel
                        ? riskBuffer.twoCrewLoadingVessel === 'on'
                        : riskAnalysis?.twoCrewLoadingVessel,
                handleChange: handleDgrFieldChange('twoCrewLoadingVessel'),
                description: (
                    <small>
                        <div>
                            Check how many people are aboard, ensure everyone is
                            accounted for.
                        </div>
                        <div>
                            Check for injuries or medical assistance required.
                        </div>
                    </small>
                ),
            },
            {
                name: 'FireHosesRiggedAndReady',
                label: 'Fire hoses rigged and ready',
                value: 'fireHosesRiggedAndReady',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.fireHosesRiggedAndReady
                        ? riskBuffer.fireHosesRiggedAndReady === 'on'
                        : riskAnalysis?.fireHosesRiggedAndReady,
                handleChange: handleDgrFieldChange('fireHosesRiggedAndReady'),
                // description commented out in original file
            },
            {
                name: 'NoSmokingSignagePosted',
                label: 'No smoking signage posted',
                value: 'noSmokingSignagePosted',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.noSmokingSignagePosted
                        ? riskBuffer.noSmokingSignagePosted === 'on'
                        : riskAnalysis?.noSmokingSignagePosted,
                handleChange: handleDgrFieldChange('noSmokingSignagePosted'),
                description: (
                    <small>
                        <div>Request that everyone wears a lifejacket.</div>
                    </small>
                ),
            },
            {
                name: 'SpillKitAvailable',
                label: 'Spill kit available',
                value: 'spillKitAvailable',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.spillKitAvailable
                        ? riskBuffer.spillKitAvailable === 'on'
                        : riskAnalysis?.spillKitAvailable,
                handleChange: handleDgrFieldChange('spillKitAvailable'),
                description: (
                    <small>
                        <div>
                            Ensure that communications have been established and
                            checked prior to beginning the tow, i.e., VHF, hand
                            signals, and/or light signals if the tow is to be
                            conducted at night.
                        </div>
                        <div>
                            Ensure there is agreement on where to tow the vessel
                            to.
                        </div>
                    </small>
                ),
            },
            {
                name: 'FireExtinguishersAvailable',
                label: 'Fire extinguishers available',
                value: 'fireExtinguishersAvailable',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.fireExtinguishersAvailable
                        ? riskBuffer.fireExtinguishersAvailable === 'on'
                        : riskAnalysis?.fireExtinguishersAvailable,
                handleChange: handleDgrFieldChange(
                    'fireExtinguishersAvailable',
                ),
                description: (
                    <small>
                        <div>Towline securely attached</div>
                        <div>
                            Ensure everything on board is stowed and secure.
                        </div>
                        <div>
                            Confirm waterline length/cruising speed of the
                            vessel (safe tow speed).
                        </div>
                        <div>Confirm attachment points for the towline.</div>
                        <div>
                            Confirm that the towline is securely attached.
                        </div>
                        <div>
                            Ensure that no one on the other vessel is in close
                            proximity to the towline before commencing the tow.
                        </div>
                        <div>
                            Turn on CRV towing lights and other vessel’s
                            navigation lights.
                        </div>
                        <div>
                            Post towline lookout with responsibility for quick
                            release of the tow / must carry or have a knife
                            handy.
                        </div>
                    </small>
                ),
            },
            {
                name: 'DGDeclarationReceived',
                label: 'DG declaration received',
                value: 'dgDeclarationReceived',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.dgDeclarationReceived
                        ? riskBuffer.dgDeclarationReceived === 'on'
                        : riskAnalysis?.dgDeclarationReceived,
                handleChange: handleDgrFieldChange('dgDeclarationReceived'),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'LoadPlanReceived',
                label: 'Load plan received',
                value: 'loadPlanReceived',
                checked:
                    isRiskBufferValid(riskBuffer) && riskBuffer.loadPlanReceived
                        ? riskBuffer.loadPlanReceived === 'on'
                        : riskAnalysis?.loadPlanReceived,
                handleChange: handleDgrFieldChange('loadPlanReceived'),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'MSDSAvailable',
                label: 'MSDS available for all dangerous goods carried',
                value: 'msdsAvailable',
                checked:
                    isRiskBufferValid(riskBuffer) && riskBuffer.msdsAvailable
                        ? riskBuffer.msdsAvailable === 'on'
                        : riskAnalysis?.msdsAvailable,
                handleChange: handleDgrFieldChange('msdsAvailable'),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'AnyVehiclesSecureToVehicleDeck',
                label: 'Any vehicles secure to vehicle deck',
                value: 'anyVehiclesSecureToVehicleDeck',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.anyVehiclesSecureToVehicleDeck
                        ? riskBuffer.anyVehiclesSecureToVehicleDeck === 'on'
                        : riskAnalysis?.anyVehiclesSecureToVehicleDeck,
                handleChange: handleDgrFieldChange(
                    'anyVehiclesSecureToVehicleDeck',
                ),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'SafetyAnnouncement',
                label: 'Safety announcement includes reference to dangerous goods & no smoking',
                value: 'safetyAnnouncement',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.safetyAnnouncement
                        ? riskBuffer.safetyAnnouncement === 'on'
                        : riskAnalysis?.safetyAnnouncement,
                handleChange: handleDgrFieldChange('safetyAnnouncement'),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
            {
                name: 'VehicleStationaryAndSecure',
                label: 'Vehicle stationary and secure prior to vehicle departing vessel',
                value: 'vehicleStationaryAndSecure',
                checked:
                    isRiskBufferValid(riskBuffer) &&
                    riskBuffer.vehicleStationaryAndSecure
                        ? riskBuffer.vehicleStationaryAndSecure === 'on'
                        : riskAnalysis?.vehicleStationaryAndSecure,
                handleChange: handleDgrFieldChange(
                    'vehicleStationaryAndSecure',
                ),
                description: (
                    <small>
                        <div>Conduct SAP prior to approaching the vessel.</div>
                        <div>
                            Check for fittings on the vessel that could damage
                            the CRV when coming alongside.
                        </div>
                    </small>
                ),
            },
        ],
        [riskBuffer, riskAnalysis],
    )

    // Memoize selected member to prevent recreation on every render
    const selectedMember = useMemo(
        () =>
            members?.find(
                (member: any) =>
                    member.value ==
                    (isRiskBufferValid(riskBuffer)
                        ? riskBuffer.memberID
                        : null),
            ) || null,
        [members, riskBuffer],
    )

    // Update the parent's "all checked" state when fields change
    useEffect(() => {
        setAllChecked(fields.every((field) => field.checked))
    }, [fields, setAllChecked])

    // Offline and online risk factors & analysis
    const offlineMount = async () => {
        const data = await models.riskFactor.getByFieldID(
            'type',
            'DangerousGoods',
        )
        const risks = Array.from(
            new Set(data.map((risk: any) => risk.title)),
        ).map((risk: any) => ({ label: risk, value: risk }))
        setAllRisks(risks)
        setAllRiskFactors(data)
    }

    useEffect(() => {
        if (offline) {
            offlineMount()
        } else {
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'DangerousGoods' } },
                },
            })
        }
    }, [])

    const offlineOpenRiskAnalysis = async () => {
        const data = await models.dangerousGoodsChecklist.getById(
            currentEvent.dangerousGoodsChecklist.id,
        )
        setRiskAnalysis(data)

        const newRiskBuffer = {
            vesselSecuredToWharf: data?.vesselSecuredToWharf ? 'on' : 'off',
            bravoFlagRaised: data?.bravoFlagRaised ? 'on' : 'off',
            twoCrewLoadingVessel: data?.twoCrewLoadingVessel ? 'on' : 'off',
            fireHosesRiggedAndReady: data?.fireHosesRiggedAndReady
                ? 'on'
                : 'off',
            noSmokingSignagePosted: data?.noSmokingSignagePosted ? 'on' : 'off',
            spillKitAvailable: data?.spillKitAvailable ? 'on' : 'off',
            fireExtinguishersAvailable: data?.fireExtinguishersAvailable
                ? 'on'
                : 'off',
            dgDeclarationReceived: data?.dgDeclarationReceived ? 'on' : 'off',
            loadPlanReceived: data?.loadPlanReceived ? 'on' : 'off',
            msdsAvailable: data?.msdsAvailable ? 'on' : 'off',
            anyVehiclesSecureToVehicleDeck: data?.anyVehiclesSecureToVehicleDeck
                ? 'on'
                : 'off',
            safetyAnnouncement: data?.safetyAnnouncement ? 'on' : 'off',
            vehicleStationaryAndSecure: data?.vehicleStationaryAndSecure
                ? 'on'
                : 'off',
            memberID: data?.member?.id || null,
        }

        setRiskBuffer(newRiskBuffer as RiskBuffer)
    }

    useEffect(() => {
        if (open) {
            const currentChecklistId = currentEvent.dangerousGoodsChecklist.id
            // Initialize local checklist ID state
            setChecklistId(currentChecklistId)
            checklistIdRef.current = currentChecklistId

            if (offline) {
                offlineOpenRiskAnalysis()
            } else {
                // Check if dangerous goods checklist exists
                // Use the current checklist ID or the previous one from ref if available
                const validChecklistId =
                    +currentChecklistId > 0
                        ? currentChecklistId
                        : +checklistIdRef.current > 0
                          ? checklistIdRef.current
                          : null

                if (validChecklistId) {
                    // Update state to use the valid ID
                    if (validChecklistId !== currentChecklistId) {
                        setChecklistId(validChecklistId)
                        checklistIdRef.current = validChecklistId
                    }
                    getRiskAnalysis({
                        variables: {
                            id: validChecklistId,
                        },
                    })
                } else {
                    // Create a new dangerous goods checklist only if we don't have one
                    createDangerousGoodsChecklist({
                        variables: {
                            input: {
                                tripReport_StopID: currentEvent.id,
                            },
                        },
                    })
                }
            }
        }
    }, [open, currentEvent.dangerousGoodsChecklist.id, currentEvent.id])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const risks = Array.from(
                new Set(
                    data.readRiskFactors.nodes?.map((risk: any) => risk.title),
                ),
            ).map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data.readRiskFactors.nodes)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [getRiskAnalysis] = useLazyQuery(GetOneDangerousGoodsChecklist, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setRiskAnalysis(data.readOneDangerousGoodsChecklist)

            const newRiskBuffer = {
                vesselSecuredToWharf: data.readOneDangerousGoodsChecklist
                    ?.vesselSecuredToWharf
                    ? 'on'
                    : 'off',
                bravoFlagRaised: data.readOneDangerousGoodsChecklist
                    ?.bravoFlagRaised
                    ? 'on'
                    : 'off',
                twoCrewLoadingVessel: data.readOneDangerousGoodsChecklist
                    ?.twoCrewLoadingVessel
                    ? 'on'
                    : 'off',
                fireHosesRiggedAndReady: data.readOneDangerousGoodsChecklist
                    ?.fireHosesRiggedAndReady
                    ? 'on'
                    : 'off',
                noSmokingSignagePosted: data.readOneDangerousGoodsChecklist
                    ?.noSmokingSignagePosted
                    ? 'on'
                    : 'off',
                spillKitAvailable: data.readOneDangerousGoodsChecklist
                    ?.spillKitAvailable
                    ? 'on'
                    : 'off',
                fireExtinguishersAvailable: data.readOneDangerousGoodsChecklist
                    ?.fireExtinguishersAvailable
                    ? 'on'
                    : 'off',
                dgDeclarationReceived: data.readOneDangerousGoodsChecklist
                    ?.dgDeclarationReceived
                    ? 'on'
                    : 'off',
                loadPlanReceived: data.readOneDangerousGoodsChecklist
                    ?.loadPlanReceived
                    ? 'on'
                    : 'off',
                msdsAvailable: data.readOneDangerousGoodsChecklist
                    ?.msdsAvailable
                    ? 'on'
                    : 'off',
                anyVehiclesSecureToVehicleDeck: data
                    .readOneDangerousGoodsChecklist
                    ?.anyVehiclesSecureToVehicleDeck
                    ? 'on'
                    : 'off',
                safetyAnnouncement: data.readOneDangerousGoodsChecklist
                    ?.safetyAnnouncement
                    ? 'on'
                    : 'off',
                vehicleStationaryAndSecure: data.readOneDangerousGoodsChecklist
                    ?.vehicleStationaryAndSecure
                    ? 'on'
                    : 'off',
                memberID:
                    data.readOneDangerousGoodsChecklist?.member?.id || null,
            } as RiskBuffer

            setRiskBuffer(newRiskBuffer)
        },
        onError: (error) => {
            console.error(`[PVPD] Error loading risk analysis data:`, error)
        },
    })

    const updateRiskAnalysisMember = useCallback(
        async (memberID: number) => {
            if (!editDGR) {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                        'You do not have permission to edit this section',
                })
                return
            }

            setRiskBuffer((prev: RiskBuffer | false) => {
                const newBuffer = {
                    ...(prev || {}),
                    memberID: memberID,
                } as RiskBuffer

                return newBuffer
            })

            const currentChecklistId = checklistIdRef.current
            if (+currentChecklistId > 0) {
                if (offline) {
                    await models.dangerousGoodsChecklist.save({
                        id: currentChecklistId,
                        memberID: memberID,
                    })
                } else {
                    updateDangerousGoodsChecklist({
                        variables: {
                            input: {
                                id: currentChecklistId,
                                memberID: memberID,
                            },
                        },
                    })
                }
            } else {
                if (!offline) {
                    createDangerousGoodsChecklist({
                        variables: {
                            input: {
                                tripReport_StopID: currentEvent.id,
                                memberID: memberID,
                            },
                        },
                    })
                }
            }
        },
        [
            editDGR,
            toast,
            currentEvent.id,
            offline,
            models.dangerousGoodsChecklist,
            updateDangerousGoodsChecklist,
            createDangerousGoodsChecklist,
        ],
    )

    const handleEditorChange = useCallback((newContent: any) => {
        setContent(newContent)
    }, [])

    const handleImpactChange = useCallback((value: any) => {
        setCurrentRisk((prev: any) => ({
            ...prev,
            impact: value?.value,
        }))
    }, [])

    const riskImpacts = useMemo(
        () => [
            { value: 'Low', label: 'Low impact' },
            { value: 'Medium', label: 'Medium impact' },
            { value: 'High', label: 'High impact' },
            { value: 'Severe', label: 'Severe impact' },
        ],
        [],
    )

    const handleSaveRisk = async () => {
        if (currentRisk.id > 0) {
            if (offline) {
                await models.riskFactor.save({
                    id: currentRisk.id,
                    type: 'DangerousGoods',
                    title: currentRisk.title,
                    impact: currentRisk?.impact ? currentRisk?.impact : 'Low',
                    probability: currentRisk?.probability
                        ? currentRisk?.probability
                        : 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    dangerousGoodsChecklistID: checklistIdRef.current,
                })
                setOpenRiskDialog(false)
            } else {
                updateRiskFactor({
                    variables: {
                        input: {
                            id: currentRisk.id,
                            type: 'DangerousGoods',
                            title: currentRisk.title,
                            impact: currentRisk?.impact
                                ? currentRisk?.impact
                                : 'Low',
                            probability: currentRisk?.probability
                                ? currentRisk?.probability
                                : 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            dangerousGoodsChecklistID: checklistIdRef.current,
                        },
                    },
                })
            }

            setAllRiskFactors(
                allRiskFactors.map((risk: any) => {
                    if (risk.id === currentRisk.id) {
                        return {
                            ...risk,
                            title: currentRisk.title,
                            impact: currentRisk.impact,
                            probability: currentRisk.probability,
                            mitigationStrategy: {
                                nodes: currentStrategies,
                            },
                        }
                    }
                    return risk
                }),
            )
            if (!allRisks.find((r: any) => r.value === currentRisk.title)) {
                setAllRisks([
                    ...allRisks,
                    { value: currentRisk.title, label: currentRisk.title },
                ])
            }
            setRiskAnalysis({
                ...riskAnalysis,
                riskFactors: {
                    nodes: riskAnalysis.riskFactors.nodes.map((risk: any) => {
                        if (risk.id === currentRisk.id) {
                            return {
                                ...risk,
                                title: currentRisk.title,
                                impact: currentRisk.impact,
                                probability: currentRisk.probability,
                                mitigationStrategy: {
                                    nodes: currentStrategies,
                                },
                            }
                        }
                        return risk
                    }),
                },
            })
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === currentRisk.title &&
                                    r.mitigationStrategy.nodes.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            if (offline) {
                const data = await models.riskFactor.save({
                    id: generateUniqueId(),
                    type: 'DangerousGoods',
                    title: currentRisk.title,
                    impact: currentRisk?.impact ? currentRisk?.impact : 'Low',
                    probability: currentRisk?.probability
                        ? currentRisk?.probability
                        : 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    dangerousGoodsChecklistID: checklistIdRef.current,
                    vesselID: vesselID,
                })
                setOpenRiskDialog(false)
                setAllRiskFactors([
                    ...allRiskFactors,
                    {
                        id: data.id,
                        title: currentRisk.title,
                        impact: currentRisk.impact,
                        probability: currentRisk.probability,
                        mitigationStrategy: {
                            nodes: currentStrategies,
                        },
                    },
                ])
                if (!allRisks.find((r: any) => r.value === currentRisk.title)) {
                    setAllRisks([
                        ...allRisks,
                        { value: currentRisk.title, label: currentRisk.title },
                    ])
                }
                setRiskAnalysis({
                    ...riskAnalysis,
                    riskFactors: {
                        nodes: [
                            ...riskAnalysis.riskFactors.nodes,
                            {
                                id: data.id,
                                title: currentRisk.title,
                                impact: currentRisk.impact,
                                probability: currentRisk.probability,
                                mitigationStrategy: {
                                    nodes: currentStrategies,
                                },
                            },
                        ],
                    },
                })
                setRecommendedStratagies(
                    Array.from(
                        new Set(
                            allRiskFactors
                                ?.filter(
                                    (r: any) =>
                                        r.title === currentRisk.title &&
                                        r.mitigationStrategy.nodes.length > 0,
                                )
                                .map((r: any) => r.mitigationStrategy.nodes)[0]
                                .map((s: any) => ({
                                    id: s.id,
                                    strategy: s.strategy,
                                })),
                        ),
                    ),
                )
            } else {
                createRiskFactor({
                    variables: {
                        input: {
                            type: 'DangerousGoods',
                            title: currentRisk.title,
                            impact: currentRisk?.impact
                                ? currentRisk?.impact
                                : 'Low',
                            probability: currentRisk?.probability
                                ? currentRisk?.probability
                                : 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            dangerousGoodsChecklistID: checklistIdRef.current,
                            vesselID: vesselID,
                        },
                    },
                })
            }
        }

        if (currentRisk?.mitigationStrategy?.id > 0) {
            if (offline) {
                await models.mitigationStrategy.save({
                    id: currentRisk.mitigationStrategy.id,
                    strategy: content,
                })
            } else {
                updateMitigationStrategy({
                    variables: {
                        input: {
                            id: currentRisk.mitigationStrategy.id,
                            strategy: content,
                        },
                    },
                })
            }
        } else {
            if (content) {
                if (offline) {
                    const data = await models.mitigationStrategy.save({
                        id: generateUniqueId(),
                        strategy: content,
                    })
                    setCurrentStrategies([
                        ...currentStrategies,
                        { id: data.id, strategy: content },
                    ])
                    setContent('')
                    setCurrentRisk({
                        ...currentRisk,
                        mitigationStrategy: data.id,
                    })
                } else {
                    createMitigationStrategy({
                        variables: {
                            input: {
                                strategy: content,
                            },
                        },
                    })
                }
            }
        }
    }

    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
            setCurrentRisk({
                ...currentRisk,
                mitigationStrategy: data.createMitigationStrategy.id,
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateMitigationStrategy] = useMutation(UpdateMitigationStrategy, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: (data) => {
            setOpenRiskDialog(false)
            setAllRiskFactors([
                ...allRiskFactors,
                {
                    id: data.createRiskFactor.id,
                    title: currentRisk.title,
                    impact: currentRisk.impact,
                    probability: currentRisk.probability,
                    mitigationStrategy: {
                        nodes: currentStrategies,
                    },
                },
            ])
            if (!allRisks.find((r: any) => r.value === currentRisk.title)) {
                setAllRisks([
                    ...allRisks,
                    { value: currentRisk.title, label: currentRisk.title },
                ])
            }
            setRiskAnalysis({
                ...riskAnalysis,
                riskFactors: {
                    nodes: [
                        ...riskAnalysis.riskFactors.nodes,
                        {
                            id: data.createRiskFactor.id,
                            title: currentRisk.title,
                            impact: currentRisk.impact,
                            probability: currentRisk.probability,
                            mitigationStrategy: {
                                nodes: currentStrategies,
                            },
                        },
                    ],
                },
            })
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === currentRisk.title &&
                                    r.mitigationStrategy.nodes.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleRiskValue = (v: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: v?.value,
        })
        setRiskValue({ value: v.value, label: v.value })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.value &&
                    risk.mitigationStrategy.nodes.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.value &&
                                    r.mitigationStrategy.nodes.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    const handleCreateRisk = (inputValue: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: inputValue,
        })
        setRiskValue({ value: inputValue, label: inputValue })
        if (allRisks) {
            const risk = [...allRisks, { value: inputValue, label: inputValue }]
            setAllRisks(risk)
        } else {
            setAllRisks([{ value: inputValue, label: inputValue }])
        }
    }

    const handleDeleteRisk = async () => {
        if (offline) {
            await models.riskFactor.save({
                id: riskToDelete.id,
                vesselID: 0,
                dangerousGoodsChecklistID: 0,
            })
            setOpenRiskDialog(false)
        } else {
            updateRiskFactor({
                variables: {
                    input: {
                        id: riskToDelete.id,
                        vesselID: 0,
                        dangerousGoodsChecklistID: 0,
                    },
                },
            })
        }

        setRiskAnalysis({
            ...riskAnalysis,
            riskFactors: {
                nodes: riskAnalysis.riskFactors.nodes.filter(
                    (risk: any) => risk.id !== riskToDelete.id,
                ),
            },
        })
        setOpenDeleteConfirmation(false)
    }

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.length > 0) {
            if (currentStrategies.find((s: any) => s.id === strategy.id)) {
                setCurrentStrategies(
                    currentStrategies.filter((s: any) => s.id !== strategy.id),
                )
            } else {
                setCurrentStrategies([...currentStrategies, strategy])
            }
        } else {
            setCurrentStrategies([strategy])
        }
    }

    const handleNewStratagy = async () => {
        if (content) {
            if (offline) {
                const data = await models.mitigationStrategy.save({
                    id: generateUniqueId(),
                    strategy: content,
                })
                setCurrentStrategies([
                    ...currentStrategies,
                    { id: data.id, strategy: content },
                ])
                setContent('')
                setCurrentRisk({
                    ...currentRisk,
                    mitigationStrategy: data.id,
                })
            } else {
                createMitigationStrategy({
                    variables: {
                        input: {
                            strategy: content,
                        },
                    },
                })
            }
        }
        setOpenRecommendedstrategy(false)
    }

    const handleSetRiskValue = (v: any) => {
        setRiskValue({
            value: v.title,
            label: v.title,
        })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy.nodes.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.title &&
                                    r.mitigationStrategy.nodes.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    return (
        <>
            <RiskAnalysisSheet
                open={open}
                onOpenChange={(isOpen) => {
                    if (onOpenChange) {
                        onOpenChange(isOpen)
                    }
                }}
                onSidebarClose={() => {
                    if (onSidebarClose) {
                        onSidebarClose()
                    }
                    if (onOpenChange) {
                        onOpenChange(false)
                    }
                }}
                title="Risk Analysis"
                subtitle="Dangerous Goods"
                checkFields={fields}
                riskFactors={riskAnalysis?.riskFactors?.nodes || []}
                crewMembers={
                    members
                        ? members.map((m: any) => ({
                              ...m,
                              value: String(m.value),
                          }))
                        : []
                }
                selectedAuthor={selectedMember}
                onAuthorChange={(value: any) => {
                    if (value) {
                        updateRiskAnalysisMember(value.value)
                    }
                }}
                canEdit={editDGR}
                canDeleteRisks={editDGR}
                onRiskClick={(risk: any) => {
                    if (!editDGR) {
                        toast({
                            variant: 'destructive',
                            title: 'Error',
                            description:
                                'You do not have permission to edit this section',
                        })
                        return
                    }
                    handleSetRiskValue(risk)
                    setCurrentRisk(risk)
                    setOpenRiskDialog(true)
                    setCurrentStrategies(risk?.mitigationStrategy?.nodes || [])
                }}
                onAddRiskClick={() => {
                    if (!editDGR) {
                        toast({
                            variant: 'destructive',
                            title: 'Error',
                            description:
                                'You do not have permission to edit this section',
                        })
                        return
                    }
                    setCurrentRisk({})
                    setContent('')
                    setRiskValue(null)
                    setOpenRiskDialog(true)
                    setCurrentStrategies([])
                }}
                onRiskDelete={(risk: any) => {
                    if (!editDGR) {
                        toast({
                            variant: 'destructive',
                            title: 'Error',
                            description:
                                'You do not have permission to edit this section',
                        })
                        return
                    }
                    setOpenDeleteConfirmation(true)
                    setRiskToDelete(risk)
                }}
                setAllChecked={setAllChecked}
            />

            <AlertDialogNew
                openDialog={openRiskDialog}
                setOpenDialog={setOpenRiskDialog}
                handleCreate={handleSaveRisk}
                actionText={currentRisk?.id > 0 ? 'Update' : 'Create Risk'}
                title={currentRisk?.id > 0 ? 'Update Risk' : 'Create New Risk'}
                variant="default"
                size="md"
                position="center"
                cancelText="Cancel">
                <div className="space-y-5">
                    {allRisks && (
                        <div>
                            <label className="text-sm font-medium">Risk</label>
                            <Creatable
                                id="risk"
                                options={allRisks}
                                menuPlacement="top"
                                placeholder="Risk"
                                value={riskValue}
                                onCreateOption={handleCreateRisk}
                                onChange={handleRiskValue}
                                classNames={{
                                    control: () =>
                                        'flex py-0.5 w-full bg-transparent rounded-lg border',
                                    singleValue: () => '',
                                    menu: () => '',
                                    option: () => '',
                                }}
                            />
                        </div>
                    )}
                    <div>
                        <label className="text-sm font-medium">
                            Risk impact
                        </label>
                        <Combobox
                            id="impact"
                            options={riskImpacts}
                            placeholder="Risk impact"
                            value={
                                currentRisk?.impact
                                    ? riskImpacts.find(
                                          (impact: any) =>
                                              impact.value ==
                                              currentRisk?.impact,
                                      )
                                    : null
                            }
                            onChange={handleImpactChange}
                        />
                    </div>
                    <div>
                        <label className="text-sm font-medium">
                            Risk probability
                        </label>
                        <div className="mt-2">
                            <input
                                type="range"
                                min="0"
                                max="10"
                                step="1"
                                value={currentRisk?.probability || 5}
                                onChange={(e) => {
                                    setCurrentRisk((prev: any) => ({
                                        ...prev,
                                        probability: parseInt(e.target.value),
                                    }))
                                }}
                                className="w-full"
                            />
                            <div className="text-sm text-muted-foreground mt-1">
                                {currentRisk?.probability || 5}/10
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className="flex items-center justify-between">
                            <label className="text-sm font-medium">
                                Mitigation strategy
                            </label>
                            <button
                                type="button"
                                onClick={() => setOpenRecommendedstrategy(true)}
                                className="text-sm text-primary hover:underline">
                                Add strategy
                            </button>
                        </div>
                        {currentStrategies.length > 0 && (
                            <div className="mt-2 space-y-2">
                                {currentStrategies.map((s: any) => (
                                    <div
                                        key={s.id}
                                        className="p-2 bg-muted/50 rounded-md">
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: s.strategy,
                                            }}
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                        {content && (
                            <div className="mt-2 p-2 bg-muted/50 rounded-md">
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: content,
                                    }}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </AlertDialogNew>
            <StrategyDialog
                open={openRecommendedstrategy}
                onOpenChange={setOpenRecommendedstrategy}
                onSave={handleNewStratagy}
                currentRisk={currentRisk}
                recommendedStrategies={recommendedStratagies}
                currentStrategies={currentStrategies}
                onStrategySelect={handleSetCurrentStrategies}
                content={content}
                onEditorChange={handleEditorChange}
            />
            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                handleCreate={handleDeleteRisk}
                actionText="Delete"
                title="Delete Risk"
                description="Are you sure you want to delete this risk? This action cannot be undone."
                variant="danger"
                size="md"
                position="center"
                showIcon={true}
            />
        </>
    )
}
