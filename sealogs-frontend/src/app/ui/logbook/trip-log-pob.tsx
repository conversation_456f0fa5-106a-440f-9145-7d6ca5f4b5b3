'use client'
import React, { useEffect, useState } from 'react'
import { UpdateTripReport_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import { useMutation } from '@apollo/client'
import { getOneClient } from '@/app/lib/actions'
import ClientModel from '@/app/offline/models/client'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
    AlertCircle,
    ArrowLeft,
    CheckCircle,
    Plus,
    PlusCircle,
    PlusIcon,
} from 'lucide-react'
import { useQueryState } from 'nuqs'

export default function POB({
    currentTrip,
    updateTripReport,
    tripReport,
    vessel,
    crewMembers,
    logBookConfig,
    masterTerm = 'Master',
    offline = false,
}: {
    currentTrip: any
    updateTripReport?: any
    tripReport: any
    vessel: any
    crewMembers: any
    logBookConfig: any
    masterTerm: string
    offline?: boolean
}) {
    const clientModel = new ClientModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const [client, setClient] = useState<any>()
    const [pob, setPOB] = useState<number>(0)
    const [totalGuests, setTotalGuests] = useState<number>(0)
    const [paxJoined, setPaxJoined] = useState<number>(0)
    const [vehicleJoined, setVehicleJoined] = useState<number>(0)

    const [, setTab] = useQueryState('tab', { defaultValue: 'crew' })

    if (!offline) {
        getOneClient(setClient)
    }
    const offlineMount = async () => {
        // getOneClient(setClient)
        const client = await clientModel.getById(
            +(localStorage.getItem('clientId') ?? 0),
        )
        setClient(client)
    }
    useEffect(() => {
        if (offline) {
            offlineMount()
        }
    }, [offline])
    const handlePOBChange = async (persons: any) => {
        const pob = +persons.target.value - paxJoined
        if (offline) {
            // updateTripReport_LogBookEntrySection
            const data = await tripReportModel.save({
                id: currentTrip.id,
                pob: pob,
            })
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), data.id],
                currentTripID: currentTrip.id,
                key: 'pob',
                value: pob,
            })
        } else {
            updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: currentTrip.id,
                        pob: pob,
                    },
                },
            })
        }
        setPOB(+persons.target.value - paxJoined)
    }

    const handlePOBValueChange = (persons: any) => {
        setPOB(+persons.target.value - paxJoined)
    }

    const [updateTripReport_LogBookEntrySection] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const setGuests = () => {
        let totalGuests = 0
        const supernumeraries = currentTrip?.tripEvents?.nodes.filter(
            (event: any) => {
                return event.eventCategory === 'Supernumerary'
            },
        )
        if (supernumeraries?.length > 0) {
            supernumeraries.forEach((s: any) => {
                totalGuests += s.supernumerary?.totalGuest || 0
            })
        }
        setTotalGuests(totalGuests)
        return totalGuests
    }

    useEffect(() => {
        if (currentTrip) {
            setPOB(Number(currentTrip?.pob ?? 0))
            setGuests()
            const paxJoined =
                currentTrip?.tripReport_Stops?.nodes.reduce(
                    (acc: number, stop: any) => {
                        return acc + stop.paxJoined - stop.paxDeparted
                    },
                    0,
                ) ?? 0
            const vehicleJoined = currentTrip?.tripReport_Stops?.nodes.reduce(
                (acc: number, stop: any) => {
                    return acc + stop.vehiclesJoined - stop.vehiclesDeparted
                },
                0,
            )
            setPaxJoined(paxJoined)
            setVehicleJoined(vehicleJoined)
        }
    }, [currentTrip])

    const crewLength = () => {
        if (!crewMembers || !Array.isArray(crewMembers)) {
            return 0
        }
        const count = crewMembers.filter(
            (member: any) =>
                member.crewMemberID > 0 && member.punchOut === null,
        ).length
        return count
    }

    const displayField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const totalPOB = pob + crewLength() + paxJoined + totalGuests

    const isOverCapacity = vessel?.maxPOB < totalPOB

    return (
        <div className="space-y-8">
            {/* Crew Section */}
            <Label
                label={`Crew (Incl. ${masterTerm})`}
                position="left"
                labelClassName="w-[200px]"
                htmlFor="crew">
                <div className="items-center space-x-2">
                    <Badge variant="outline">{crewLength()}</Badge>
                    <Button className="border" onClick={() => setTab('crew')}>
                        Add crew
                    </Button>
                </div>
            </Label>

            {/* Passengers Section */}
            <Label
                label="Passengers on board"
                htmlFor="pob"
                position="left"
                labelClassName="w-[200px]"
                className="flex items-center justify-between">
                <Input
                    id="pob"
                    name="pob"
                    type="number"
                    value={
                        isNaN(+pob) || isNaN(+paxJoined) ? 0 : +pob + +paxJoined
                    }
                    className="w-20"
                    required
                    min={isNaN(paxJoined) ? 0 : paxJoined}
                    onBlur={handlePOBChange}
                    onChange={handlePOBValueChange}
                />
            </Label>

            {/* Supernumerary Section - Conditionally Rendered */}
            {displayField('EventSupernumerary') && (
                <Label
                    label="Supernumerary"
                    htmlFor="supernumerary"
                    position="left"
                    labelClassName="w-[200px]">
                    <div className="items-center space-x-2">
                        <Badge variant="outline">
                            {isNaN(totalGuests) ? 0 : totalGuests}
                        </Badge>{' '}
                        <span>total guests</span>
                    </div>
                </Label>
            )}

            <Label
                label="Total P.O.B:"
                htmlFor="totalPob"
                position="left"
                labelClassName="w-[200px]">
                <Badge variant={isOverCapacity ? 'destructive' : 'success'}>
                    {isNaN(totalPOB) ? 0 : totalPOB}
                </Badge>
            </Label>

            {isOverCapacity && (
                <div className="text-destructive text-sm">
                    WARNING: Your total P.O.B exceeds your max P.O.B as setup in
                    your vessel config
                </div>
            )}
        </div>
    )
}
